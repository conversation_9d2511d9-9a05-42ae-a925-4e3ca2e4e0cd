{
  "nbformat": 4,
  "nbformat_minor": 0,
  "metadata": {
    "colab": {
      "provenance": [],
      "gpuType": "T4",
      "include_colab_link": true
    },
    "kernelspec": {
      "name": "python3",
      "display_name": "Python 3"
    },
    "language_info": {
      "name": "python"
    },
    "accelerator": "GPU"
  },
  "cells": [
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "view-in-github",
        "colab_type": "text"
      },
      "source": [
        "<a href=\"https://colab.research.google.com/github/ace-step/ACE-Step/blob/main/inference.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"
      ]
    },
    {
      "cell_type": "markdown",
      "source": [
        "# ACE-Step Inference\n",
        "\n",
        "\n",
        "<p align=\"center\">\n",
        "    <img src=\"https://raw.githubusercontent.com/ace-step/ACE-Step/refs/heads/main/assets/orgnization_logos.png\" width=\"100%\" alt=\"StepFun Logo\">\n",
        "</p>\n",
        "\n",
        " A Step Towards Music Generation Foundation Model\n",
        "\n",
        "\n",
        "\n",
        "## Credits:\n",
        "\n",
        "* Ace-Step by [Ace-Step](https://github.com/ace-step/ACE-Step)\n",
        "\n",
        "* Colab improvement by [NeoDev](https://github.com/TheNeodev)"
      ],
      "metadata": {
        "id": "w4sQAC7AB5GV"
      }
    },
    {
      "cell_type": "markdown",
      "source": [
        "**🖥️ Hardware Performance**\n",
        "\n",
        "We have evaluated ACE-Step across different hardware setups, yielding the following throughput results:\n",
        "\n",
        "| Device          | RTF (27 steps) | Time to render 1 min audio (27 steps) | RTF (60 steps) | Time to render 1 min audio (60 steps) |\n",
        "| --------------- | -------------- | ------------------------------------- | -------------- | ------------------------------------- |\n",
        "| NVIDIA RTX 4090 | 34.48 ×        | 1.74 s                                | 15.63 ×        | 3.84 s                                |\n",
        "| NVIDIA A100     | 27.27 ×        | 2.20 s                                | 12.27 ×        | 4.89 s                                |\n",
        "| NVIDIA RTX 3090 | 12.76 ×        | 4.70 s                                | 6.48 ×         | 9.26 s                                |\n",
        "| MacBook M2 Max  | 2.27 ×         | 26.43 s                               | 1.03 ×         | 58.25 s                               |\n",
        "\n",
        "\n",
        "We use RTF (Real-Time Factor) to measure the performance of ACE-Step. Higher values indicate faster generation speed. 27.27x means to generate 1 minute of music, it takes 2.2 seconds (60/27.27). The performance is measured on a single GPU with batch size 1 and 27 steps."
      ],
      "metadata": {
        "id": "pXuz4oKlDFk_"
      }
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "cellView": "form",
        "id": "EB_ztF9xch5s"
      },
      "outputs": [],
      "source": [
        "#@title 🎯 Install and Download\n",
        "\n",
        "from IPython.display import clear_output"
        "\n",
        "import codecs\n",
        "\n",
        "\n",
        "\n",
        "print(\"Installing...\")\n",
        "!sudo apt update > /dev/null 2>&1\n",
        "!sudo apt install python3.10 > /dev/null 2>&1\n",
        "!sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1 > /dev/null 2>&1\n",
        "!sudo update-alternatives --set python3 /usr/bin/python3.10 > /dev/null 2>&1\n",
        "!curl -sS https://bootstrap.pypa.io/get-pip.py | python3 > /dev/null 2>&1\n",
        "import sys\n",
        "sys.path.append('/usr/local/lib/python3.10/dist-packages')\n",
        "\n",
        "\n",
        "repopath = codecs.decode('erdhverzragf.gkg', 'rot_13')\n",
        "\n",
        "\n",
        "!git clone https://github.com/ace-step/ACE-Step\n",
        "%cd /content/ACE-Step\n",
        "\n",
        "\n",
        "!pip install uv pyngrok > /dev/null 2>&1\n",
        "!uv pip install -r {repopath} > /dev/null 2>&1\n",
        "!uv pip install huggingface-hub numpy==1.26.0 > /dev/null 2>&1\n",
        "!huggingface-cli download ACE-Step/ACE-Step-v1-3.5B --local-dir /content/ACE-Step/checkpoints --local-dir-use-symlinks False\n",
        "\n",
        "!pip install e .",
        "\n",
        "\n",
        "import os\n",
        "os.environ['MPLBACKEND'] = 'agg'"
        "\n\n"
        "clear_output()"
        "\n\n"
        "print("✅ Installation Complete!")"
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "#@title 🚀 Run Gradio UI\n",
        "bf16 = True # @param {\"type\":\"boolean\"}\n",
        "\n",
        "print(\" 🚀 Running UI...\")\n",
        "\n",
        "!acestep --checkpoint_path ./checkpoints/ --port 7865 --device_id 0 --share true --bf16 {bf16}"
      ],
      "metadata": {
        "cellView": "form",
        "id": "0m835b3ZecQW"
      },
      "execution_count": null,
      "outputs": []
    }
  ]
}
