{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4", "private_outputs": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/ace-step/ACE-Step/blob/main/colab_inference.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Install"], "metadata": {"id": "_sjfo37-gDQV"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0W0bvPq1df_a"}, "outputs": [], "source": ["#!pip uninstall ace-step -y\n", "!pip install --upgrade git+https://github.com/ace-step/ACE-Step.git\n", "import os\n", "os.environ['ACE_PIPELINE_DTYPE'] = 'float16'"]}, {"cell_type": "markdown", "source": ["# Import Model From GDrive (Optional)"], "metadata": {"id": "2FQ5E6MvgJ09"}}, {"cell_type": "code", "source": ["from google.colab import drive\n", "drive.mount('/gdrive')\n", "!unzip /gdrive/MyDrive/acestep/checkpoints.zip -d /unzip"], "metadata": {"id": "QZjFgQxGgOdc"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# Run Interface"], "metadata": {"id": "TYaWXOLcgO4A"}}, {"cell_type": "code", "source": ["torch_compile = True # @param {type: \"boolean\"}\n", "cpu_offload = False # @param {type: \"boolean\"}\n", "overlapped_decode = True # @param {type: \"boolean\"}\n", "#bf16 = True # @param {type: \"boolean\"}\n", "\n", "!acestep --checkpoint_path /unzip/checkpoints/ --port 7865 --device_id 0 --share true --torch_compile {torch_compile} --cpu_offload {cpu_offload} --overlapped_decode {overlapped_decode}"], "metadata": {"id": "Q9S6FxllgPHw"}, "execution_count": null, "outputs": []}]}