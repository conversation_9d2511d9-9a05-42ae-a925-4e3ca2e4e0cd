
services:
  &name ace-step:
    build:
      context: https://github.com/ace-step/ACE-Step.git
      dockerfile: Dockerfile
    container_name: *name
    hostname: *name
    stop_grace_period: 2s
    ports:
      - "7865:7865"
    volumes:
      - ./checkpoints:/app/checkpoints
      - ./outputs:/app/outputs
      - ./logs:/app/logs
    environment:
      - ACE_OUTPUT_DIR=/app/outputs
    # command: python app.py --server_name 0.0.0.0 --port 7865 --share False --bf16 True --torch_compile True --device-id 0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7865/"]
      interval: 60s
      timeout: 10s
      retries: 30
      start_period: 3s
    restart: unless-stopped
    runtime: nvidia
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: ["compute", "utility", "graphics", "video"]
