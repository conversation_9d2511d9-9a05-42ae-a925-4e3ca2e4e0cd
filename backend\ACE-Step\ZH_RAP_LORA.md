# 🎤 RapMachine Release

We meticulously curated and trained this model on Chinese rap/hip-hop datasets, with rigorous data cleaning and recaptioning. The results include:  
- **Improved pronunciation** for Chinese lyrics  
- **Enhanced adherence** to hip-hop and electronic music styles  
- **Greater diversity** in hip-hop vocal performances  

### **How to Use**  
1. Generate **higher-quality Chinese songs**  (⚠️ It's not just for Chinese songs. You can also use it in other ways. )

2. Create **better hip-hop tracks**  
3. Blend it with other genres to:  
   - Produce music with **richer vocal details**  
   - Experiment with **underground or street culture flavors**  
4. Fine-tune outputs using the following dimensions:  

    - **`vocal_timbre`**: Describes the inherent qualities of the voice.
        - Examples: *Bright, dark, warm, cold, breathy, nasal, gritty, smooth, husky, metallic, whispery, resonant, airy, smoky, sultry, light, clear, high-pitched, raspy, powerful, ethereal, flute-like, hollow, velvety, shrill, hoarse, mellow, thin, thick, reedy, silvery, twangy.*  

    - **`techniques`**:
        - Examples:  
            - **Rap styles**: `mumble rap`, `chopper rap`, `melodic rap`, `lyrical rap`, `trap flow`, `double-time rap`  
            - **Vocal effects**: `auto-tune`, `reverb`, `delay`, `distortion`  
            - **Delivery styles**: `whispered`, `shouted`, `spoken word`, `narration`, `singing`  
            - **Other vocalizations**: `ad-libs`, `call-and-response`, `harmonized`  

---  

## Community Note
We’ve **revamped and expanded** the LoRA training guide with finer details. This release is a **proof of concept**—showcasing the potential of **ACE-Step**.

While a Chinese rap LoRA might seem niche for non-Chinese communities, we consistently demonstrate through such projects that ACE-step - as a music generation foundation model - holds boundless potential. It doesn't just improve pronunciation in one language, but spawns new styles. 

The universal human appreciation of music is a precious asset. Like abstract LEGO blocks, these elements will eventually combine in more organic ways. May our open-source contributions propel the evolution of musical history forward.

**Enjoy it, customize it, and create something entirely new.** 

We can’t wait to hear what you’ll build!  
